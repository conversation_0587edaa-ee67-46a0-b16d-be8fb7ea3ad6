:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: inherit;
  overflow-x: hidden;
}

#root {
  min-height: 100vh;
}

/* 确保图片不会超出屏幕 */
img {
  max-width: 100%;
  height: auto;
}

/* 移除默认的按钮样式 */
button {
  border: none;
  background: none;
  font-family: inherit;
  cursor: pointer;
  outline: none;
}

/* 移除默认的链接样式 */
a {
  text-decoration: none;
  color: inherit;
}

/* 确保触摸设备上的点击体验 */
@media (hover: none) and (pointer: coarse) {
  button:hover {
    transform: none !important;
  }
}

/* 防止用户选择文本（在图片浏览时） */
.viewer {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许在首页选择文本 */
.home {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
