/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 加载动画 */
.loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: white;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 首页样式 */
.home {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.home-content {
  text-align: center;
  color: white;
  max-width: 500px;
  width: 100%;
}

.home-content h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.home-content p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.difficulty-buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.difficulty-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px 40px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  width: 100%;
  max-width: 300px;
  min-height: 120px;
  justify-content: center;
}

.difficulty-btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.difficulty-btn:active {
  transform: translateY(-2px);
}

.btn-icon {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.btn-text {
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.btn-desc {
  font-size: 0.9rem;
  opacity: 0.7;
}

.easy-btn:hover {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.hard-btn:hover {
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* 图片浏览器样式 */
.viewer {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f8f9fa;
}

.viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  background: #667eea;
  color: white;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: #5a67d8;
  transform: translateX(-2px);
}

.progress-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  text-align: right;
}

.difficulty-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 2px;
}

.page-info {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.progress-bar {
  height: 4px;
  background: #e2e8f0;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

/* 图片容器 */
.image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 0;
}

.word-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  transition: transform 0.3s ease;
}

.word-image:hover {
  transform: scale(1.02);
}

/* 导航按钮 */
.navigation {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  background: white;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
}

.nav-btn {
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 120px;
}

.prev-btn {
  background: #e2e8f0;
  color: #4a5568;
}

.prev-btn:not(:disabled):hover {
  background: #cbd5e0;
  transform: translateX(-3px);
}

.next-btn {
  background: #667eea;
  color: white;
}

.next-btn:not(:disabled):hover {
  background: #5a67d8;
  transform: translateX(3px);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.help-text {
  text-align: center;
  padding: 10px 20px;
  background: white;
  color: #666;
  font-size: 0.9rem;
}

.help-text p {
  margin: 0;
}

/* 错误页面 */
.error {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  color: white;
  text-align: center;
}

.error button {
  margin-top: 20px;
  padding: 10px 20px;
  border: none;
  border-radius: 25px;
  background: white;
  color: #333;
  cursor: pointer;
  font-size: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .home-content h1 {
    font-size: 2rem;
  }

  .difficulty-btn {
    padding: 25px 30px;
    min-height: 100px;
  }

  .btn-icon {
    font-size: 2rem;
  }

  .btn-text {
    font-size: 1.2rem;
  }

  .viewer-header {
    padding: 10px 15px;
  }

  .back-btn {
    padding: 8px 16px;
    font-size: 0.9rem;
  }

  .image-container {
    padding: 15px;
  }

  .navigation {
    padding: 15px;
  }

  .nav-btn {
    padding: 12px 20px;
    min-width: 100px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .home-content h1 {
    font-size: 1.8rem;
  }

  .difficulty-buttons {
    gap: 15px;
  }

  .difficulty-btn {
    padding: 20px 25px;
    min-height: 90px;
  }

  .navigation {
    flex-direction: column;
    gap: 10px;
  }

  .nav-btn {
    width: 100%;
  }
}
