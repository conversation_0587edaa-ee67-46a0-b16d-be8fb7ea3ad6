<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词长图浏览器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #000; color: white; overflow: hidden;
        }
        
        /* 首页样式 */
        .home { 
            display: flex; justify-content: center; align-items: center;
            min-height: 100vh; padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .home-content { text-align: center; color: white; max-width: 500px; width: 100%; }
        .home h1 { 
            font-size: 2.5rem; margin-bottom: 20px; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .home p { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; }
        .difficulty-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .btn { 
            display: flex; flex-direction: column; align-items: center;
            padding: 25px 40px; border: none; border-radius: 15px;
            background: rgba(255, 255, 255, 0.95); color: #333;
            cursor: pointer; transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%; max-width: 300px; min-height: 100px;
        }
        .btn:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .btn-icon { font-size: 2rem; margin-bottom: 8px; }
        .btn-text { font-size: 1.3rem; font-weight: 600; margin-bottom: 5px; }
        .btn-desc { font-size: 0.9rem; opacity: 0.7; }
        
        /* 长图浏览器样式 */
        .viewer { 
            display: none; position: fixed; top: 0; left: 0; 
            width: 100vw; height: 100vh; background: #000;
            z-index: 1000;
        }
        .viewer.active { display: flex; flex-direction: column; }
        
        /* 顶部控制栏 */
        .top-bar { 
            position: fixed; top: 0; left: 0; right: 0; 
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .top-bar.hidden { opacity: 0; pointer-events: none; }
        
        .back-btn { 
            background: #667eea; color: white; padding: 8px 16px;
            border: none; border-radius: 20px; cursor: pointer; font-size: 0.9rem;
        }
        .progress-info { text-align: right; color: white; }
        .difficulty-label { font-size: 0.8rem; opacity: 0.8; margin-bottom: 2px; }
        .page-info { font-size: 1rem; font-weight: 600; }
        
        /* 图片容器 */
        .image-container { 
            flex: 1; width: 100%; height: 100vh;
            overflow-y: auto; overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
        }
        .image-container::-webkit-scrollbar { display: none; } /* Chrome/Safari */
        
        .word-image {
            width: 70%; height: auto; display: block;
            max-width: none; max-height: none;
            margin: 0 auto; /* 居中显示 */
        }
        
        /* 底部控制栏 */
        .bottom-bar { 
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .bottom-bar.hidden { opacity: 0; pointer-events: none; }
        
        .nav-btn { 
            background: rgba(255,255,255,0.2); color: white; 
            border: none; border-radius: 20px; padding: 10px 20px;
            cursor: pointer; font-size: 0.9rem; font-weight: 600;
            transition: all 0.3s ease; min-width: 80px;
        }
        .nav-btn:hover { background: rgba(255,255,255,0.3); }
        .nav-btn:disabled { opacity: 0.3; cursor: not-allowed; }
        
        .zoom-controls { display: flex; gap: 10px; align-items: center; }
        .zoom-btn { 
            background: rgba(255,255,255,0.2); color: white;
            border: none; border-radius: 50%; width: 40px; height: 40px;
            cursor: pointer; font-size: 1.2rem; display: flex;
            align-items: center; justify-content: center;
        }
        .zoom-btn:hover { background: rgba(255,255,255,0.3); }
        
        /* 状态提示 */
        .status { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white;
            padding: 20px 30px; border-radius: 10px;
            font-size: 1rem; text-align: center; z-index: 1002;
        }
        .loading { color: #667eea; }
        .error { color: #ff6b6b; }
        
        /* 操作提示 */
        .tip { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9); color: white;
            padding: 20px 30px; border-radius: 15px;
            font-size: 0.9rem; text-align: center; z-index: 1002;
            max-width: 80%; opacity: 0; transition: opacity 0.3s ease;
        }
        .tip.show { opacity: 1; }
        
        /* 手机端优化 */
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .btn { padding: 20px 30px; min-height: 90px; }
            .btn-icon { font-size: 1.8rem; }
            .btn-text { font-size: 1.1rem; }
            .top-bar, .bottom-bar { padding: 10px 15px; }
            .back-btn { padding: 6px 12px; font-size: 0.8rem; }
            .nav-btn { padding: 8px 16px; font-size: 0.8rem; min-width: 70px; }
            .zoom-btn { width: 35px; height: 35px; font-size: 1rem; }
        }
    </style>
</head>
<body>
    <div id="home" class="home">
        <div class="home-content">
            <h1>📚 英语单词长图浏览器</h1>
            <p>专为长图设计的浏览器</p>
            <div class="difficulty-buttons">
                <button class="btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📖</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习 (104张长图)</span>
                </button>
                <button class="btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战 (89张长图)</span>
                </button>
            </div>
        </div>
    </div>

    <div id="viewer" class="viewer">
        <div id="top-bar" class="top-bar">
            <button class="back-btn" onclick="goHome()">← 返回</button>
            <div class="progress-info">
                <div class="difficulty-label" id="difficulty-label">简单</div>
                <div class="page-info" id="page-info">1 / 1</div>
            </div>
        </div>
        
        <div class="image-container" id="image-container">
            <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
        </div>
        
        <div id="bottom-bar" class="bottom-bar">
            <button class="nav-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="resetZoom()" title="重置">⌂</button>
                <button class="zoom-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
            </div>
            <button class="nav-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
        </div>
        
        <div id="status" class="status" style="display: none;">正在加载图片...</div>
        <div id="tip" class="tip">
            📱 长图浏览提示：<br>
            • 上下滑动查看完整内容<br>
            • 左右滑动切换图片<br>
            • 点击屏幕隐藏/显示控制栏<br>
            • 双击图片重置位置
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];
        let controlsVisible = true;
        let hideControlsTimer = null;

        // 新的英文命名图片列表
        const easyImages = [
            "easy_001.png", "easy_002.png", "easy_003.png", "easy_004.png", "easy_005.png",
            "easy_006.png", "easy_007.png", "easy_008.png", "easy_009.png", "easy_010.png",
            "easy_011.png", "easy_012.png", "easy_013.png", "easy_014.png", "easy_015.png",
            "easy_016.png", "easy_017.png", "easy_018.png", "easy_019.png", "easy_020.png",
            "easy_021.png", "easy_022.png", "easy_023.png", "easy_024.png", "easy_025.png",
            "easy_026.png", "easy_027.png", "easy_028.png", "easy_029.png", "easy_030.png",
            "easy_031.png", "easy_032.png", "easy_033.png", "easy_034.png", "easy_035.png",
            "easy_036.png", "easy_037.png", "easy_038.png", "easy_039.png", "easy_040.png",
            "easy_041.png", "easy_042.png", "easy_043.png", "easy_044.png", "easy_045.png",
            "easy_046.png", "easy_047.png", "easy_048.png", "easy_049.png", "easy_050.png",
            "easy_051.png", "easy_052.png", "easy_053.png", "easy_054.png", "easy_055.png",
            "easy_056.png", "easy_057.png", "easy_058.png", "easy_059.png", "easy_060.png",
            "easy_061.png", "easy_062.png", "easy_063.png", "easy_064.png", "easy_065.png",
            "easy_066.png", "easy_067.png", "easy_068.png", "easy_069.png", "easy_070.png",
            "easy_071.png", "easy_072.png", "easy_073.png", "easy_074.png", "easy_075.png",
            "easy_076.png", "easy_077.png", "easy_078.png", "easy_079.png", "easy_080.png",
            "easy_081.png", "easy_082.png", "easy_083.png", "easy_084.png", "easy_085.png",
            "easy_086.png", "easy_087.png", "easy_088.png", "easy_089.png", "easy_090.png",
            "easy_091.png", "easy_092.png", "easy_093.png", "easy_094.png", "easy_095.png",
            "easy_096.png", "easy_097.png", "easy_098.png", "easy_099.png", "easy_100.png",
            "easy_101.png", "easy_102.png", "easy_103.png", "easy_104.png"
        ];

        const hardImages = [
            "hard_001.png", "hard_002.png", "hard_003.png", "hard_004.png", "hard_005.png",
            "hard_006.png", "hard_007.png", "hard_008.png", "hard_009.png", "hard_010.png",
            "hard_011.png", "hard_012.png", "hard_013.png", "hard_014.png", "hard_015.png",
            "hard_016.png", "hard_017.png", "hard_018.png", "hard_019.png", "hard_020.png",
            "hard_021.png", "hard_022.png", "hard_023.png", "hard_024.png", "hard_025.png",
            "hard_026.png", "hard_027.png", "hard_028.png", "hard_029.png", "hard_030.png",
            "hard_031.png", "hard_032.png", "hard_033.png", "hard_034.png", "hard_035.png",
            "hard_036.png", "hard_037.png", "hard_038.png", "hard_039.png", "hard_040.png",
            "hard_041.png", "hard_042.png", "hard_043.png", "hard_044.png", "hard_045.png",
            "hard_046.png", "hard_047.png", "hard_048.png", "hard_049.png", "hard_050.png",
            "hard_051.png", "hard_052.png", "hard_053.png", "hard_054.png", "hard_055.png",
            "hard_056.png", "hard_057.png", "hard_058.png", "hard_059.png", "hard_060.png",
            "hard_061.png", "hard_062.png", "hard_063.png", "hard_064.png", "hard_065.png",
            "hard_066.png", "hard_067.png", "hard_068.png", "hard_069.png", "hard_070.png",
            "hard_071.png", "hard_072.png", "hard_073.png", "hard_074.png", "hard_075.png",
            "hard_076.png", "hard_077.png", "hard_078.png", "hard_079.png", "hard_080.png",
            "hard_081.png", "hard_082.png", "hard_083.png", "hard_084.png", "hard_085.png",
            "hard_086.png", "hard_087.png", "hard_088.png", "hard_089.png"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
                showTip();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            
            // 更新进度信息
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            
            // 更新图片
            const imgElement = document.getElementById('word-image');
            const statusElement = document.getElementById('status');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '正在加载图片...';
            
            // 设置图片加载事件
            imgElement.onload = function() {
                statusElement.style.display = 'none';
                imgElement.style.display = 'block';
                resetZoom();
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                statusElement.className = 'status error';
                statusElement.textContent = `图片加载失败: ${currentImage}`;
            };
            
            // 设置图片路径 - 更新为新的路径
            imgElement.src = `images/${currentView}/${currentImage}`;
            
            // 更新导航按钮
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'flex';
            document.getElementById('viewer').classList.remove('active');
        }

        function resetZoom() {
            const container = document.getElementById('image-container');
            container.scrollTop = 0;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function showTip() {
            const tip = document.getElementById('tip');
            tip.classList.add('show');
            setTimeout(() => {
                tip.classList.remove('show');
            }, 5000);
        }

        function toggleControls() {
            const topBar = document.getElementById('top-bar');
            const bottomBar = document.getElementById('bottom-bar');
            
            controlsVisible = !controlsVisible;
            
            if (controlsVisible) {
                topBar.classList.remove('hidden');
                bottomBar.classList.remove('hidden');
            } else {
                topBar.classList.add('hidden');
                bottomBar.classList.add('hidden');
            }
        }

        function autoHideControls() {
            if (hideControlsTimer) {
                clearTimeout(hideControlsTimer);
            }
            
            hideControlsTimer = setTimeout(() => {
                if (controlsVisible) {
                    toggleControls();
                }
            }, 3000);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    toggleControls();
                }
            }
        });

        // 触摸手势支持
        let touchStartX = null;
        let touchStartY = null;
        let isScrolling = false;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isScrolling = false;
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartX === null || touchStartY === null) return;
            
            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            const diffX = Math.abs(touchStartX - touchCurrentX);
            const diffY = Math.abs(touchStartY - touchCurrentY);
            
            if (diffY > diffX) {
                isScrolling = true;
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null || touchStartY === null || isScrolling) {
                touchStartX = null;
                touchStartY = null;
                isScrolling = false;
                return;
            }
            
            const touchEndX = e.changedTouches[0].clientX;
            const diffX = touchStartX - touchEndX;
            
            if (Math.abs(diffX) > 80) {
                if (diffX > 0) {
                    goToNext();
                } else {
                    goToPrevious();
                }
            }
            
            touchStartX = null;
            touchStartY = null;
            isScrolling = false;
        });

        // 点击切换控制栏
        document.getElementById('image-container').addEventListener('click', function(e) {
            if (e.target.tagName === 'IMG') {
                toggleControls();
            }
        });

        // 双击重置位置
        document.getElementById('word-image').addEventListener('dblclick', function() {
            resetZoom();
        });

        // 鼠标移动时显示控制栏
        document.addEventListener('mousemove', function() {
            if (!controlsVisible) {
                toggleControls();
            }
            autoHideControls();
        });
    </script>
</body>
</html>
