<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词长图浏览器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #000; color: white; overflow: hidden;
        }
        
        /* 首页样式 */
        .home { 
            display: flex; justify-content: center; align-items: center;
            min-height: 100vh; padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .home-content { text-align: center; color: white; max-width: 500px; width: 100%; }
        .home h1 { 
            font-size: 2.5rem; margin-bottom: 20px; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .home p { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; }
        .difficulty-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .btn { 
            display: flex; flex-direction: column; align-items: center;
            padding: 25px 40px; border: none; border-radius: 15px;
            background: rgba(255, 255, 255, 0.95); color: #333;
            cursor: pointer; transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%; max-width: 300px; min-height: 100px;
        }
        .btn:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .btn-icon { font-size: 2rem; margin-bottom: 8px; }
        .btn-text { font-size: 1.3rem; font-weight: 600; margin-bottom: 5px; }
        .btn-desc { font-size: 0.9rem; opacity: 0.7; }
        
        /* 长图浏览器样式 */
        .viewer { 
            display: none; position: fixed; top: 0; left: 0; 
            width: 100vw; height: 100vh; background: #000;
            z-index: 1000;
        }
        .viewer.active { display: flex; flex-direction: column; }
        
        /* 顶部控制栏 */
        .top-bar { 
            position: fixed; top: 0; left: 0; right: 0; 
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .top-bar.hidden { opacity: 0; pointer-events: none; }
        
        .back-btn { 
            background: #667eea; color: white; padding: 8px 16px;
            border: none; border-radius: 20px; cursor: pointer; font-size: 0.9rem;
        }
        .progress-info { text-align: right; color: white; }
        .difficulty-label { font-size: 0.8rem; opacity: 0.8; margin-bottom: 2px; }
        .page-info { font-size: 1rem; font-weight: 600; }
        
        /* 图片容器 */
        .image-container { 
            flex: 1; width: 100%; height: 100vh;
            overflow-y: auto; overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
        }
        .image-container::-webkit-scrollbar { display: none; } /* Chrome/Safari */
        
        .word-image { 
            width: 100%; height: auto; display: block;
            max-width: none; max-height: none;
        }
        
        /* 底部控制栏 */
        .bottom-bar { 
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .bottom-bar.hidden { opacity: 0; pointer-events: none; }
        
        .nav-btn { 
            background: rgba(255,255,255,0.2); color: white; 
            border: none; border-radius: 20px; padding: 10px 20px;
            cursor: pointer; font-size: 0.9rem; font-weight: 600;
            transition: all 0.3s ease; min-width: 80px;
        }
        .nav-btn:hover { background: rgba(255,255,255,0.3); }
        .nav-btn:disabled { opacity: 0.3; cursor: not-allowed; }
        
        .zoom-controls { display: flex; gap: 10px; align-items: center; }
        .zoom-btn { 
            background: rgba(255,255,255,0.2); color: white;
            border: none; border-radius: 50%; width: 40px; height: 40px;
            cursor: pointer; font-size: 1.2rem; display: flex;
            align-items: center; justify-content: center;
        }
        .zoom-btn:hover { background: rgba(255,255,255,0.3); }
        
        /* 状态提示 */
        .status { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white;
            padding: 20px 30px; border-radius: 10px;
            font-size: 1rem; text-align: center; z-index: 1002;
        }
        .loading { color: #667eea; }
        .error { color: #ff6b6b; }
        
        /* 操作提示 */
        .tip { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9); color: white;
            padding: 20px 30px; border-radius: 15px;
            font-size: 0.9rem; text-align: center; z-index: 1002;
            max-width: 80%; opacity: 0; transition: opacity 0.3s ease;
        }
        .tip.show { opacity: 1; }
        
        /* 手机端优化 */
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .btn { padding: 20px 30px; min-height: 90px; }
            .btn-icon { font-size: 1.8rem; }
            .btn-text { font-size: 1.1rem; }
            .top-bar, .bottom-bar { padding: 10px 15px; }
            .back-btn { padding: 6px 12px; font-size: 0.8rem; }
            .nav-btn { padding: 8px 16px; font-size: 0.8rem; min-width: 70px; }
            .zoom-btn { width: 35px; height: 35px; font-size: 1rem; }
        }
    </style>
</head>
<body>
    <div id="home" class="home">
        <div class="home-content">
            <h1>📚 英语单词长图浏览器</h1>
            <p>专为长图设计的浏览器</p>
            <div class="difficulty-buttons">
                <button class="btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📖</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习 (104张长图)</span>
                </button>
                <button class="btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战 (89张长图)</span>
                </button>
            </div>
        </div>
    </div>

    <div id="viewer" class="viewer">
        <div id="top-bar" class="top-bar">
            <button class="back-btn" onclick="goHome()">← 返回</button>
            <div class="progress-info">
                <div class="difficulty-label" id="difficulty-label">简单</div>
                <div class="page-info" id="page-info">1 / 1</div>
            </div>
        </div>
        
        <div class="image-container" id="image-container">
            <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
        </div>
        
        <div id="bottom-bar" class="bottom-bar">
            <button class="nav-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="resetZoom()" title="重置">⌂</button>
                <button class="zoom-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
            </div>
            <button class="nav-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
        </div>
        
        <div id="status" class="status" style="display: none;">正在加载图片...</div>
        <div id="tip" class="tip">
            📱 长图浏览提示：<br>
            • 上下滑动查看完整内容<br>
            • 左右滑动切换图片<br>
            • 点击屏幕隐藏/显示控制栏<br>
            • 双击图片重置位置
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];
        let controlsVisible = true;
        let hideControlsTimer = null;

        // 图片列表（与之前相同）
        const easyImages = [
            "1.单词这样记1 .png", "1.单词这样记2 .png", "1.最强记忆法1 .png", "1.最强记忆法2 .png",
            "10-麦当劳1 .png", "10-麦当劳2 .png", "10-麦当劳3 .png", "11-皮皮虾1 .png", "11-皮皮虾2 .png",
            "12-光棍1  .png", "12-光棍1 .png", "12-光棍2 .png", "13-吝啬鬼1 .png", "13-吝啬鬼2 .png",
            "13-吝啬鬼3 .png", "13-吝啬鬼4 .png", "14-桃花1 .PNG", "14-桃花2 .PNG", "14-桃花3 .PNG",
            "15-嫦娥1 .PNG", "15-嫦娥2 .PNG", "15-嫦娥3 .PNG", "16-日月星辰1更多课程lxknumber1 .PNG",
            "16-日月星辰2更多课程lxknumber1 .PNG", "17-男明星1 .PNG", "17-男明星2 .PNG", "17-男明星3 .PNG",
            "18-女明星1 .png", "18-女明星2 .png", "18-女明星3 .png", "19-歌手1 .png", "19-歌手2 .png",
            "19-歌手3 .png", "19-歌手4 .png", "2-女王 .png", "2-女王2 .png", "2-女王3 .png", "2-女王4 .png",
            "20-石头1 .png", "20-石头2 .png", "20-石头3 .png", "20-石头4 .png", "21-小猫1 .png",
            "21-小猫2 .png", "21-小猫3 .png", "22-街景1 .png", "22-街景2 .png", "22-街景3 .png",
            "22-街景4 .png", "22-街景5 .png", "23-战争1 .png", "23-战争2 .png", "23-战争3 .png",
            "24-学校1 .png", "24-学校2 .png", "24-学校3 .png", "24-学校4 .png", "25-工作1 .png",
            "25-工作2 .png", "25-工作3 .png", "25-工作4 .png", "26-吃货1更多课程lxknumber1 .png",
            "26-吃货2更多课程lxknumber1 .png", "26-吃货3更多课程lxknumber1 .png", "26-吃货4更多课程lxknumber1 .png",
            "27-家居1 .png", "27-家居2 .png", "27-家居3 .png", "28-小威1 .png", "28-小威2 .png",
            "28-小威3 .png", "28-小威4 .png", "29-化妆品1 .png", "29-化妆品2 .png", "3-佛1 .png",
            "3-佛2 .png", "30-汽车1 .png", "30-汽车2 .png", "31-手机1 .png", "31-手机2 .png",
            "31-手机3 .png", "32-人生1 .png", "32-人生2 .png", "33-熟词词根1 .png", "33-熟词词根2 .png",
            "33-熟词词根3 .png", "33-熟词词根4 .png", "34-后缀1 .png", "34-后缀2 .png", "34-后缀3  .png",
            "34-后缀4 .png", "35-前缀1 .png", "35-前缀2 .png", "4-白娘子1 .png", "4-白娘子2 .png",
            "4-白娘子3 .png", "4-白娘子4 .png", "5-小哥哥 .png", "5-小哥哥2 .png", "5-小哥哥3 .png",
            "6-小姐姐1更多课程lxknumber1 .png", "6-小姐姐2更多课程lxknumber1 .png", "7-爸妈1 .png",
            "7-爸妈2 .png", "7-爸妈3 .png", "7-爸妈4 .png", "8-弟弟1 .png", "8-弟弟2 .png",
            "8-弟弟3 .png", "8-弟弟4 .png", "8-弟弟5 .png", "9-歌舞1 .png", "9-歌舞2 .png", "9-歌舞3 .png"
        ];

        const hardImages = [
            "1.单词这么记 .PNG", "1.最强记忆法1 .PNG", "1.最强记忆法2 .PNG", "10.猫1 .PNG", "10.猫2 .PNG",
            "10.猫3 .PNG", "11.动物园1 .PNG", "11.动物园2 .PNG", "11.动物园3 .PNG", "12.太阳1 .PNG",
            "12.太阳2 .PNG", "13.门前1 .PNG", "13.门前2 .PNG", "14.风景1 .PNG", "14.风景2 .PNG",
            "15.小威（上）1 .PNG", "15.小威（上）2 .PNG", "15.小威（上）3 .PNG", "15.小威（上）4 .PNG",
            "16.小威（下）1更多课程lxknumber1 .PNG", "16.小威（下）2更多课程lxknumber1 .PNG", "16.小威（下）3更多课程lxknumber1 .PNG",
            "17.自然1 .PNG", "17.自然2 .PNG", "17.自然3 .PNG", "17.自然4 .PNG", "17.自然5 .PNG",
            "18.课堂1 .PNG", "18.课堂2 .PNG", "19.工人1 .PNG", "19.工人2 .PNG", "2.白娘子1 .PNG",
            "2.白娘子2 .PNG", "2.白娘子3 .PNG", "2.白娘子4 .PNG", "20.大牌1 .PNG", "20.大牌2 .PNG",
            "20.大牌3 .PNG", "20.大牌4 .PNG", "21.麦当劳1 .PNG", "21.麦当劳2 .PNG", "21.麦当劳3 .PNG",
            "22.零食1 .PNG", "22.零食2 .PNG", "22.零食3 .PNG", "22.零食4 .PNG", "23.买买提1 .PNG",
            "23.买买提2 .PNG", "23.买买提3 .PNG", "24.唱跳1 .PNG", "24.唱跳2 .PNG", "25.小东西1 .PNG",
            "25.小东西2 .PNG", "26.熟词词根1更多课程lxknumber1 .PNG", "26.熟词词根2更多课程lxknumber1 .PNG",
            "26.熟词词根3更多课程lxknumber1 .PNG", "26.熟词词根4更多课程lxknumber1 .PNG", "26.熟词词根5更多课程lxknumber1 .PNG",
            "26.熟词词根6更多课程lxknumber1 .PNG", "26.熟词词根7更多课程lxknumber1 .PNG", "27.词缀1 .PNG",
            "27.词缀2 .PNG", "27.词缀3 .PNG", "27.词缀4 .PNG", "27.词缀5 .PNG", "27.词缀6更多课程lxknumber1 .PNG",
            "3.嫦娥1 .PNG", "3.嫦娥2 .PNG", "3.嫦娥3 .PNG", "4.哥哥1 .PNG", "4.哥哥2 .PNG",
            "5.弟弟1 .PNG", "5.弟弟2 .PNG", "5.弟弟3 .PNG", "6.妈妈1更多课程lxknumber1 .PNG",
            "6.妈妈2更多课程lxknumber1 .PNG", "6.妈妈3更多课程lxknumber1 .PNG", "7.女王1 .PNG",
            "7.女王2 .PNG", "7.女王3 .PNG", "7.女王4 .PNG", "8.男明星1 .PNG", "8.男明星2 .PNG",
            "9.女明星1 .PNG", "9.女明星2 .PNG", "9.女明星3 .PNG", "9.女明星4 .PNG", "9.女明星5 .PNG"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
                showTip();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            
            // 更新进度信息
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            
            // 更新图片
            const imgElement = document.getElementById('word-image');
            const statusElement = document.getElementById('status');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '正在加载图片...';
            
            // 设置图片加载事件
            imgElement.onload = function() {
                statusElement.style.display = 'none';
                imgElement.style.display = 'block';
                resetZoom();
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                statusElement.className = 'status error';
                statusElement.textContent = `图片加载失败: ${currentImage}`;
            };
            
            // 设置图片路径
            imgElement.src = `public/images/${currentView}/${currentImage}`;
            
            // 更新导航按钮
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'flex';
            document.getElementById('viewer').classList.remove('active');
        }

        function resetZoom() {
            const container = document.getElementById('image-container');
            container.scrollTop = 0;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function showTip() {
            const tip = document.getElementById('tip');
            tip.classList.add('show');
            setTimeout(() => {
                tip.classList.remove('show');
            }, 5000);
        }

        function toggleControls() {
            const topBar = document.getElementById('top-bar');
            const bottomBar = document.getElementById('bottom-bar');
            
            controlsVisible = !controlsVisible;
            
            if (controlsVisible) {
                topBar.classList.remove('hidden');
                bottomBar.classList.remove('hidden');
            } else {
                topBar.classList.add('hidden');
                bottomBar.classList.add('hidden');
            }
        }

        function autoHideControls() {
            if (hideControlsTimer) {
                clearTimeout(hideControlsTimer);
            }
            
            hideControlsTimer = setTimeout(() => {
                if (controlsVisible) {
                    toggleControls();
                }
            }, 3000);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    toggleControls();
                }
            }
        });

        // 触摸手势支持
        let touchStartX = null;
        let touchStartY = null;
        let isScrolling = false;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isScrolling = false;
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartX === null || touchStartY === null) return;
            
            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            const diffX = Math.abs(touchStartX - touchCurrentX);
            const diffY = Math.abs(touchStartY - touchCurrentY);
            
            if (diffY > diffX) {
                isScrolling = true;
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null || touchStartY === null || isScrolling) {
                touchStartX = null;
                touchStartY = null;
                isScrolling = false;
                return;
            }
            
            const touchEndX = e.changedTouches[0].clientX;
            const diffX = touchStartX - touchEndX;
            
            if (Math.abs(diffX) > 80) {
                if (diffX > 0) {
                    goToNext();
                } else {
                    goToPrevious();
                }
            }
            
            touchStartX = null;
            touchStartY = null;
            isScrolling = false;
        });

        // 点击切换控制栏
        document.getElementById('image-container').addEventListener('click', function(e) {
            if (e.target.tagName === 'IMG') {
                toggleControls();
            }
        });

        // 双击重置位置
        document.getElementById('word-image').addEventListener('dblclick', function() {
            resetZoom();
        });

        // 鼠标移动时显示控制栏
        document.addEventListener('mousemove', function() {
            if (!controlsVisible) {
                toggleControls();
            }
            autoHideControls();
        });
    </script>
</body>
</html>
