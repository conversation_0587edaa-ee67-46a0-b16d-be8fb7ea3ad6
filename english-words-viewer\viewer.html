<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词图片浏览器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; 
        }
        .container { max-width: 900px; margin: 0 auto; padding: 20px; }
        .home { text-align: center; color: white; padding: 50px 20px; }
        .home h1 { 
            font-size: 2.5rem; margin-bottom: 20px; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .home p { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; }
        .difficulty-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .btn { 
            display: flex; flex-direction: column; align-items: center;
            padding: 25px 40px; border: none; border-radius: 15px;
            background: rgba(255, 255, 255, 0.95); color: #333;
            cursor: pointer; transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%; max-width: 300px; min-height: 100px;
        }
        .btn:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .btn-icon { font-size: 2rem; margin-bottom: 8px; }
        .btn-text { font-size: 1.3rem; font-weight: 600; margin-bottom: 5px; }
        .btn-desc { font-size: 0.9rem; opacity: 0.7; }
        
        .viewer {
            display: none; background: white; border-radius: 15px;
            overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            height: 100vh; /* 全屏高度 */
        }
        .viewer.active { display: flex; flex-direction: column; }
        
        .header { 
            display: flex; justify-content: space-between; align-items: center;
            padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6;
        }
        .back-btn { 
            background: #667eea; color: white; padding: 10px 20px;
            border: none; border-radius: 25px; cursor: pointer; font-size: 1rem;
            transition: all 0.3s ease;
        }
        .back-btn:hover { background: #5a67d8; }
        .progress-info { text-align: right; }
        .difficulty-label { font-size: 0.9rem; color: #666; margin-bottom: 2px; }
        .page-info { font-size: 1.1rem; font-weight: 600; color: #333; }
        
        .progress-bar { height: 4px; background: #e2e8f0; position: relative; }
        .progress-fill { 
            height: 100%; background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease; width: 0%;
        }
        
        .image-container {
            text-align: center; padding: 0;
            display: flex; align-items: flex-start; justify-content: center; flex-direction: column;
            overflow-y: auto; overflow-x: hidden;
            height: calc(100vh - 200px); /* 减去头部和底部的高度 */
        }
        .word-image {
            width: 70%; height: auto;
            max-width: none; max-height: none;
            border-radius: 0; margin: 0 auto;
            box-shadow: none; transition: none;
            display: block;
        }
        .word-image:hover { transform: none; }

        /* 长图专用样式 */
        .image-scroll-container {
            width: 100%; height: 100%;
            overflow-y: auto; overflow-x: hidden;
            -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
            scrollbar-width: thin; /* Firefox */
        }

        /* 自定义滚动条样式 */
        .image-scroll-container::-webkit-scrollbar {
            width: 8px;
        }
        .image-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .image-scroll-container::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }
        .image-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #5a67d8;
        }
        .status { margin-top: 20px; font-size: 1rem; }
        .loading { color: #667eea; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        
        .navigation { 
            display: flex; justify-content: space-between; padding: 20px;
            background: #f8f9fa; border-top: 1px solid #dee2e6;
        }
        .nav-btn { 
            padding: 12px 25px; border: none; border-radius: 25px;
            cursor: pointer; font-size: 1rem; font-weight: 600;
            transition: all 0.3s ease; min-width: 120px;
        }
        .prev-btn { background: #e2e8f0; color: #4a5568; }
        .prev-btn:not(:disabled):hover { background: #cbd5e0; transform: translateX(-3px); }
        .next-btn { background: #667eea; color: white; }
        .next-btn:not(:disabled):hover { background: #5a67d8; transform: translateX(3px); }
        .nav-btn:disabled { opacity: 0.5; cursor: not-allowed; transform: none !important; }
        
        .help-text {
            text-align: center; padding: 10px 20px; background: #f8f9fa;
            color: #666; font-size: 0.9rem; border-top: 1px solid #dee2e6;
            flex-shrink: 0; /* 防止被压缩 */
        }

        /* 长图提示 */
        .long-image-tip {
            position: absolute; top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white;
            padding: 15px 20px; border-radius: 10px;
            font-size: 0.9rem; text-align: center;
            z-index: 1000; pointer-events: none;
            opacity: 0; transition: opacity 0.3s ease;
        }
        .long-image-tip.show { opacity: 1; }
        .long-image-tip.hide { opacity: 0; }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1); border-radius: 10px;
            padding: 20px; margin-top: 30px; color: white;
        }
        .instructions h3 { margin-bottom: 15px; }
        .instructions ol { margin-left: 20px; }
        .instructions li { margin-bottom: 8px; }
        
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .container { padding: 10px; }
            .btn { padding: 20px 30px; min-height: 90px; }
            .btn-icon { font-size: 1.8rem; }
            .btn-text { font-size: 1.1rem; }
            .navigation { flex-direction: column; gap: 10px; }
            .nav-btn { width: 100%; min-width: auto; }
            .image-container {
                padding: 0;
                height: calc(100vh - 180px); /* 手机上调整高度 */
            }
            .viewer { border-radius: 0; } /* 手机上去掉圆角 */
            .container { padding: 0; } /* 手机上去掉边距 */
            .help-text { font-size: 0.8rem; padding: 8px 15px; }
            .long-image-tip {
                font-size: 0.8rem;
                padding: 10px 15px;
                max-width: 80%;
            }
        }

        @media (max-width: 480px) {
            .image-container {
                height: calc(100vh - 160px); /* 小屏手机进一步调整 */
            }
            .header { padding: 10px 15px; }
            .back-btn { padding: 8px 16px; font-size: 0.9rem; }
            .page-info { font-size: 1rem; }
            .difficulty-label { font-size: 0.8rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="home" class="home">
            <h1>📚 英语单词图片浏览器</h1>
            <p>选择难度开始学习</p>
            <div class="difficulty-buttons">
                <button class="btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📖</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习 (104张图片)</span>
                </button>
                <button class="btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战 (89张图片)</span>
                </button>
            </div>
            
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <ol>
                    <li>点击上方按钮选择难度级别</li>
                    <li>使用"上一张"/"下一张"按钮或键盘方向键翻页</li>
                    <li>在手机上可以左右滑动翻页</li>
                    <li>按ESC键或点击"返回"按钮回到首页</li>
                    <li>如果图片无法显示，请确保图片文件在正确的文件夹中</li>
                </ol>
            </div>
        </div>

        <div id="viewer" class="viewer">
            <div class="header">
                <button class="back-btn" onclick="goHome()">← 返回</button>
                <div class="progress-info">
                    <div class="difficulty-label" id="difficulty-label">简单</div>
                    <div class="page-info" id="page-info">1 / 1</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="image-container">
                <div class="image-scroll-container">
                    <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
                </div>
                <div id="status" class="status loading" style="display: none;">正在加载图片...</div>
            </div>
            
            <div class="navigation">
                <button class="nav-btn prev-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
                <button class="nav-btn next-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
            </div>
            
            <div class="help-text">
                <p>💡 提示：这是长图，可以上下滚动查看完整内容。使用键盘方向键或滑动手势翻页，按ESC返回首页</p>
            </div>

            <!-- 长图提示 -->
            <div id="long-image-tip" class="long-image-tip">
                📱 这是长图，可以上下滚动查看<br>
                ⬅️➡️ 左右滑动切换图片
            </div>
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];

        // 新的英文命名图片列表
        const easyImages = [
            "easy_001.png", "easy_002.png", "easy_003.png", "easy_004.png", "easy_005.png",
            "easy_006.png", "easy_007.png", "easy_008.png", "easy_009.png", "easy_010.png",
            "easy_011.png", "easy_012.png", "easy_013.png", "easy_014.png", "easy_015.png",
            "easy_016.png", "easy_017.png", "easy_018.png", "easy_019.png", "easy_020.png",
            "easy_021.png", "easy_022.png", "easy_023.png", "easy_024.png", "easy_025.png",
            "easy_026.png", "easy_027.png", "easy_028.png", "easy_029.png", "easy_030.png",
            "easy_031.png", "easy_032.png", "easy_033.png", "easy_034.png", "easy_035.png",
            "easy_036.png", "easy_037.png", "easy_038.png", "easy_039.png", "easy_040.png",
            "easy_041.png", "easy_042.png", "easy_043.png", "easy_044.png", "easy_045.png",
            "easy_046.png", "easy_047.png", "easy_048.png", "easy_049.png", "easy_050.png",
            "easy_051.png", "easy_052.png", "easy_053.png", "easy_054.png", "easy_055.png",
            "easy_056.png", "easy_057.png", "easy_058.png", "easy_059.png", "easy_060.png",
            "easy_061.png", "easy_062.png", "easy_063.png", "easy_064.png", "easy_065.png",
            "easy_066.png", "easy_067.png", "easy_068.png", "easy_069.png", "easy_070.png",
            "easy_071.png", "easy_072.png", "easy_073.png", "easy_074.png", "easy_075.png",
            "easy_076.png", "easy_077.png", "easy_078.png", "easy_079.png", "easy_080.png",
            "easy_081.png", "easy_082.png", "easy_083.png", "easy_084.png", "easy_085.png",
            "easy_086.png", "easy_087.png", "easy_088.png", "easy_089.png", "easy_090.png",
            "easy_091.png", "easy_092.png", "easy_093.png", "easy_094.png", "easy_095.png",
            "easy_096.png", "easy_097.png", "easy_098.png", "easy_099.png", "easy_100.png",
            "easy_101.png", "easy_102.png", "easy_103.png", "easy_104.png"
        ];

        const hardImages = [
            "hard_001.png", "hard_002.png", "hard_003.png", "hard_004.png", "hard_005.png",
            "hard_006.png", "hard_007.png", "hard_008.png", "hard_009.png", "hard_010.png",
            "hard_011.png", "hard_012.png", "hard_013.png", "hard_014.png", "hard_015.png",
            "hard_016.png", "hard_017.png", "hard_018.png", "hard_019.png", "hard_020.png",
            "hard_021.png", "hard_022.png", "hard_023.png", "hard_024.png", "hard_025.png",
            "hard_026.png", "hard_027.png", "hard_028.png", "hard_029.png", "hard_030.png",
            "hard_031.png", "hard_032.png", "hard_033.png", "hard_034.png", "hard_035.png",
            "hard_036.png", "hard_037.png", "hard_038.png", "hard_039.png", "hard_040.png",
            "hard_041.png", "hard_042.png", "hard_043.png", "hard_044.png", "hard_045.png",
            "hard_046.png", "hard_047.png", "hard_048.png", "hard_049.png", "hard_050.png",
            "hard_051.png", "hard_052.png", "hard_053.png", "hard_054.png", "hard_055.png",
            "hard_056.png", "hard_057.png", "hard_058.png", "hard_059.png", "hard_060.png",
            "hard_061.png", "hard_062.png", "hard_063.png", "hard_064.png", "hard_065.png",
            "hard_066.png", "hard_067.png", "hard_068.png", "hard_069.png", "hard_070.png",
            "hard_071.png", "hard_072.png", "hard_073.png", "hard_074.png", "hard_075.png",
            "hard_076.png", "hard_077.png", "hard_078.png", "hard_079.png", "hard_080.png",
            "hard_081.png", "hard_082.png", "hard_083.png", "hard_084.png", "hard_085.png",
            "hard_086.png", "hard_087.png", "hard_088.png", "hard_089.png"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            const progress = ((currentImageIndex + 1) / images.length) * 100;
            
            // 更新进度信息
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            
            // 更新图片
            const imgElement = document.getElementById('word-image');
            const statusElement = document.getElementById('status');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '正在加载图片...';
            
            // 设置图片加载事件
            imgElement.onload = function() {
                statusElement.style.display = 'none';
                imgElement.style.display = 'block';

                // 重置滚动位置到顶部
                const scrollContainer = document.querySelector('.image-scroll-container');
                if (scrollContainer) {
                    scrollContainer.scrollTop = 0;
                }

                // 显示长图提示
                showLongImageTip();
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                statusElement.className = 'status error';
                statusElement.textContent = `图片加载失败: ${currentImage}`;
                
                // 尝试其他可能的路径
                const currentSrc = imgElement.src;
                if (currentSrc.includes('public/images/')) {
                    imgElement.src = `images/${currentView}/${currentImage}`;
                } else if (currentSrc.includes('images/')) {
                    imgElement.src = `${currentView}/${currentImage}`;
                } else {
                    statusElement.textContent = `无法找到图片: ${currentImage}`;
                }
            };
            
            // 设置图片路径 - 更新为新的路径
            imgElement.src = `images/${currentView}/${currentImage}`;
            
            // 更新导航按钮
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'block';
            document.getElementById('viewer').classList.remove('active');
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            }
        });

        // 长图提示功能
        function showLongImageTip() {
            const tip = document.getElementById('long-image-tip');
            if (tip) {
                tip.classList.add('show');
                setTimeout(() => {
                    tip.classList.remove('show');
                    tip.classList.add('hide');
                }, 3000); // 3秒后隐藏
            }
        }

        // 触摸手势支持 - 针对长图优化
        let touchStartX = null;
        let touchStartY = null;
        let isScrolling = false;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isScrolling = false;
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartX === null || touchStartY === null) return;

            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            const diffX = Math.abs(touchStartX - touchCurrentX);
            const diffY = Math.abs(touchStartY - touchCurrentY);

            // 如果垂直滑动距离大于水平滑动，认为是在滚动图片
            if (diffY > diffX) {
                isScrolling = true;
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null || touchStartY === null || isScrolling) {
                touchStartX = null;
                touchStartY = null;
                isScrolling = false;
                return;
            }

            const touchEndX = e.changedTouches[0].clientX;
            const diffX = touchStartX - touchEndX;

            // 只有在水平滑动距离足够大时才切换图片
            if (Math.abs(diffX) > 80) {
                if (diffX > 0) {
                    goToNext(); // 向左滑动，下一张
                } else {
                    goToPrevious(); // 向右滑动，上一张
                }
            }

            touchStartX = null;
            touchStartY = null;
            isScrolling = false;
        });
    </script>
</body>
</html>
