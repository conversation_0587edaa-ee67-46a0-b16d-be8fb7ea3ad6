<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词图片浏览器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; 
        }
        .container { max-width: 900px; margin: 0 auto; padding: 20px; }
        .home { text-align: center; color: white; padding: 50px 20px; }
        .home h1 { 
            font-size: 2.5rem; margin-bottom: 20px; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .home p { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; }
        .difficulty-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .btn { 
            display: flex; flex-direction: column; align-items: center;
            padding: 25px 40px; border: none; border-radius: 15px;
            background: rgba(255, 255, 255, 0.95); color: #333;
            cursor: pointer; transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%; max-width: 300px; min-height: 100px;
        }
        .btn:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .btn-icon { font-size: 2rem; margin-bottom: 8px; }
        .btn-text { font-size: 1.3rem; font-weight: 600; margin-bottom: 5px; }
        .btn-desc { font-size: 0.9rem; opacity: 0.7; }
        
        .viewer {
            display: none; background: white; border-radius: 15px;
            overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            height: 100vh; /* 全屏高度 */
        }
        .viewer.active { display: flex; flex-direction: column; }
        
        .header { 
            display: flex; justify-content: space-between; align-items: center;
            padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6;
        }
        .back-btn { 
            background: #667eea; color: white; padding: 10px 20px;
            border: none; border-radius: 25px; cursor: pointer; font-size: 1rem;
            transition: all 0.3s ease;
        }
        .back-btn:hover { background: #5a67d8; }
        .progress-info { text-align: right; }
        .difficulty-label { font-size: 0.9rem; color: #666; margin-bottom: 2px; }
        .page-info { font-size: 1.1rem; font-weight: 600; color: #333; }
        
        .progress-bar { height: 4px; background: #e2e8f0; position: relative; }
        .progress-fill { 
            height: 100%; background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease; width: 0%;
        }
        
        .image-container {
            text-align: center; padding: 0;
            display: flex; align-items: flex-start; justify-content: center; flex-direction: column;
            overflow-y: auto; overflow-x: hidden;
            height: calc(100vh - 200px); /* 减去头部和底部的高度 */
        }
        .word-image {
            width: 70%; height: auto;
            max-width: none; max-height: none;
            border-radius: 0; margin: 0 auto;
            box-shadow: none; transition: none;
            display: block;
        }
        .word-image:hover { transform: none; }

        /* 长图专用样式 */
        .image-scroll-container {
            width: 100%; height: 100%;
            overflow-y: auto; overflow-x: hidden;
            -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
            scrollbar-width: thin; /* Firefox */
        }

        /* 自定义滚动条样式 */
        .image-scroll-container::-webkit-scrollbar {
            width: 8px;
        }
        .image-scroll-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }
        .image-scroll-container::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 4px;
        }
        .image-scroll-container::-webkit-scrollbar-thumb:hover {
            background: #5a67d8;
        }
        .status { margin-top: 20px; font-size: 1rem; }
        .loading { color: #667eea; }
        .error { color: #dc3545; }
        .success { color: #28a745; }
        
        .navigation { 
            display: flex; justify-content: space-between; padding: 20px;
            background: #f8f9fa; border-top: 1px solid #dee2e6;
        }
        .nav-btn { 
            padding: 12px 25px; border: none; border-radius: 25px;
            cursor: pointer; font-size: 1rem; font-weight: 600;
            transition: all 0.3s ease; min-width: 120px;
        }
        .prev-btn { background: #e2e8f0; color: #4a5568; }
        .prev-btn:not(:disabled):hover { background: #cbd5e0; transform: translateX(-3px); }
        .next-btn { background: #667eea; color: white; }
        .next-btn:not(:disabled):hover { background: #5a67d8; transform: translateX(3px); }
        .nav-btn:disabled { opacity: 0.5; cursor: not-allowed; transform: none !important; }
        
        .help-text {
            text-align: center; padding: 10px 20px; background: #f8f9fa;
            color: #666; font-size: 0.9rem; border-top: 1px solid #dee2e6;
            flex-shrink: 0; /* 防止被压缩 */
        }

        /* 长图提示 */
        .long-image-tip {
            position: absolute; top: 50%; left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white;
            padding: 15px 20px; border-radius: 10px;
            font-size: 0.9rem; text-align: center;
            z-index: 1000; pointer-events: none;
            opacity: 0; transition: opacity 0.3s ease;
        }
        .long-image-tip.show { opacity: 1; }
        .long-image-tip.hide { opacity: 0; }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1); border-radius: 10px;
            padding: 20px; margin-top: 30px; color: white;
        }
        .instructions h3 { margin-bottom: 15px; }
        .instructions ol { margin-left: 20px; }
        .instructions li { margin-bottom: 8px; }
        
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .container { padding: 10px; }
            .btn { padding: 20px 30px; min-height: 90px; }
            .btn-icon { font-size: 1.8rem; }
            .btn-text { font-size: 1.1rem; }
            .navigation { flex-direction: column; gap: 10px; }
            .nav-btn { width: 100%; min-width: auto; }
            .image-container {
                padding: 0;
                height: calc(100vh - 180px); /* 手机上调整高度 */
            }
            .viewer { border-radius: 0; } /* 手机上去掉圆角 */
            .container { padding: 0; } /* 手机上去掉边距 */
            .help-text { font-size: 0.8rem; padding: 8px 15px; }
            .long-image-tip {
                font-size: 0.8rem;
                padding: 10px 15px;
                max-width: 80%;
            }
        }

        @media (max-width: 480px) {
            .image-container {
                height: calc(100vh - 160px); /* 小屏手机进一步调整 */
            }
            .header { padding: 10px 15px; }
            .back-btn { padding: 8px 16px; font-size: 0.9rem; }
            .page-info { font-size: 1rem; }
            .difficulty-label { font-size: 0.8rem; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="home" class="home">
            <h1>📚 英语单词图片浏览器</h1>
            <p>选择难度开始学习</p>
            <div class="difficulty-buttons">
                <button class="btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📖</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习 (115张图片)</span>
                </button>
                <button class="btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战 (89张图片)</span>
                </button>
            </div>
            
            <div class="instructions">
                <h3>📋 使用说明</h3>
                <ol>
                    <li>点击上方按钮选择难度级别</li>
                    <li>使用"上一张"/"下一张"按钮或键盘方向键翻页</li>
                    <li>在手机上可以左右滑动翻页</li>
                    <li>按ESC键或点击"返回"按钮回到首页</li>
                    <li>如果图片无法显示，请确保图片文件在正确的文件夹中</li>
                </ol>
            </div>
        </div>

        <div id="viewer" class="viewer">
            <div class="header">
                <button class="back-btn" onclick="goHome()">← 返回</button>
                <div class="progress-info">
                    <div class="difficulty-label" id="difficulty-label">简单</div>
                    <div class="page-info" id="page-info">1 / 1</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="image-container">
                <div class="image-scroll-container">
                    <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
                </div>
                <div id="status" class="status loading" style="display: none;">正在加载图片...</div>
            </div>
            
            <div class="navigation">
                <button class="nav-btn prev-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
                <button class="nav-btn next-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
            </div>
            
            <div class="help-text">
                <p>💡 提示：这是长图，可以上下滚动查看完整内容。使用键盘方向键或滑动手势翻页，按ESC返回首页</p>
            </div>

            <!-- 长图提示 -->
            <div id="long-image-tip" class="long-image-tip">
                📱 这是长图，可以上下滚动查看<br>
                ⬅️➡️ 左右滑动切换图片
            </div>
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];

        // 根据你的自定义命名更新的图片列表
        const easyImages = [
            "01.png", "02.png", "03.png", "04.png", "05.png", "06.png", "07.png", "08.png", "09.png",
            "010.png", "011.png", "012.png", "013.png", "014.png", "015.png", "016.png", "017.png", "018.png", "019.png",
            "020.png", "021.png", "022.png", "023.png", "024.png", "025.png", "026.png", "027.png", "028.png", "029.png",
            "030.png", "031.png", "032.png", "033.png", "034.png", "035.png", "036.png", "037.png", "038.png", "039.png",
            "040.png", "041.png", "042.png", "043.png", "044.png", "045.png", "046.png", "047.PNG", "048.PNG", "049.PNG",
            "050.PNG", "051.PNG", "052.PNG", "053.PNG", "054.PNG", "055.PNG", "056.PNG", "057.PNG", "058.png", "059.png",
            "060.png", "061.png", "062.png", "063.png", "064.png", "065.png", "066.png", "067.png", "068.png", "069.png",
            "070.png", "071.png", "072.png", "073.png", "074.png", "075.png", "076.png", "077.png", "078.png", "079.png",
            "080.png", "081.png", "082.png", "083.png", "084.png", "085.png", "086.png", "087.png", "088.png", "089.png",
            "090.png", "091.png", "092.png", "093.png", "094.png", "095.png", "096.png", "097.png", "098.png", "099.png",
            "0100.png", "0101.png", "0102.png", "0103.png", "0104.png", "0105.png", "0106.png", "0107.png", "0108.png", "0109.png",
            "0110.png", "0111.png", "0112.png", "0113.png", "0114.png"
        ];

        const hardImages = [
            "01.PNG", "02.PNG", "03.PNG", "04.PNG", "05.PNG", "06.PNG", "07.PNG", "08.PNG", "09.PNG",
            "010.PNG", "011.PNG", "012.PNG", "013.PNG", "014.PNG", "015.PNG", "016.PNG", "017.PNG", "018.PNG", "019.PNG",
            "020.PNG", "021.PNG", "022.PNG", "023.PNG", "024.PNG", "025.PNG", "026.PNG", "027.PNG", "028.PNG", "029.PNG",
            "030.PNG", "031.PNG", "032.PNG", "033.PNG", "034.PNG", "035.PNG", "036.PNG", "037.PNG", "038.PNG", "039.PNG",
            "040.PNG", "041.PNG", "042.PNG", "043.PNG", "044.PNG", "045.PNG", "046.PNG", "047.PNG", "048.PNG", "049.PNG",
            "050.PNG", "051.PNG", "052.PNG", "053.PNG", "054.PNG", "055.PNG", "056.PNG", "057.PNG", "058.PNG", "059.PNG",
            "060.PNG", "061.PNG", "062.PNG", "063.PNG", "064.PNG", "065.PNG", "066.PNG", "067.PNG", "068.PNG", "069.PNG",
            "070.PNG", "071.PNG", "072.PNG", "073.PNG", "074.PNG", "075.PNG", "076.PNG", "077.PNG", "078.PNG", "079.PNG",
            "080.PNG", "081.PNG", "082.PNG", "083.PNG", "084.PNG", "085.PNG", "086.PNG", "087.PNG", "088.PNG"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            const progress = ((currentImageIndex + 1) / images.length) * 100;
            
            // 更新进度信息
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            
            // 更新图片
            const imgElement = document.getElementById('word-image');
            const statusElement = document.getElementById('status');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '正在加载图片...';
            
            // 设置图片加载事件
            imgElement.onload = function() {
                statusElement.style.display = 'none';
                imgElement.style.display = 'block';

                // 重置滚动位置到顶部
                const scrollContainer = document.querySelector('.image-scroll-container');
                if (scrollContainer) {
                    scrollContainer.scrollTop = 0;
                }

                // 显示长图提示
                showLongImageTip();
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                statusElement.className = 'status error';
                statusElement.textContent = `图片加载失败: ${currentImage}`;
                
                // 尝试其他可能的路径
                const currentSrc = imgElement.src;
                if (currentSrc.includes('public/images/')) {
                    imgElement.src = `images/${currentView}/${currentImage}`;
                } else if (currentSrc.includes('images/')) {
                    imgElement.src = `${currentView}/${currentImage}`;
                } else {
                    statusElement.textContent = `无法找到图片: ${currentImage}`;
                }
            };
            
            // 设置图片路径 - 使用你的自定义路径
            imgElement.src = `public/images/${currentView}/${currentImage}`;
            
            // 更新导航按钮
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'block';
            document.getElementById('viewer').classList.remove('active');
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            }
        });

        // 长图提示功能
        function showLongImageTip() {
            const tip = document.getElementById('long-image-tip');
            if (tip) {
                tip.classList.add('show');
                setTimeout(() => {
                    tip.classList.remove('show');
                    tip.classList.add('hide');
                }, 3000); // 3秒后隐藏
            }
        }

        // 触摸手势支持 - 针对长图优化
        let touchStartX = null;
        let touchStartY = null;
        let isScrolling = false;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isScrolling = false;
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartX === null || touchStartY === null) return;

            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            const diffX = Math.abs(touchStartX - touchCurrentX);
            const diffY = Math.abs(touchStartY - touchCurrentY);

            // 如果垂直滑动距离大于水平滑动，认为是在滚动图片
            if (diffY > diffX) {
                isScrolling = true;
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null || touchStartY === null || isScrolling) {
                touchStartX = null;
                touchStartY = null;
                isScrolling = false;
                return;
            }

            const touchEndX = e.changedTouches[0].clientX;
            const diffX = touchStartX - touchEndX;

            // 只有在水平滑动距离足够大时才切换图片
            if (Math.abs(diffX) > 80) {
                if (diffX > 0) {
                    goToNext(); // 向左滑动，下一张
                } else {
                    goToPrevious(); // 向右滑动，上一张
                }
            }

            touchStartX = null;
            touchStartY = null;
            isScrolling = false;
        });
    </script>
</body>
</html>
