# 重命名和整理图片脚本

Write-Host "开始重命名和整理图片..." -ForegroundColor Green

# 简单单词图片重命名映射
$easyImages = @(
    "easy_001.png", "easy_002.png", "easy_003.png", "easy_004.png", "easy_005.png",
    "easy_006.png", "easy_007.png", "easy_008.png", "easy_009.png", "easy_010.png",
    "easy_011.png", "easy_012.png", "easy_013.png", "easy_014.png", "easy_015.png",
    "easy_016.png", "easy_017.png", "easy_018.png", "easy_019.png", "easy_020.png",
    "easy_021.png", "easy_022.png", "easy_023.png", "easy_024.png", "easy_025.png",
    "easy_026.png", "easy_027.png", "easy_028.png", "easy_029.png", "easy_030.png",
    "easy_031.png", "easy_032.png", "easy_033.png", "easy_034.png", "easy_035.png",
    "easy_036.png", "easy_037.png", "easy_038.png", "easy_039.png", "easy_040.png",
    "easy_041.png", "easy_042.png", "easy_043.png", "easy_044.png", "easy_045.png",
    "easy_046.png", "easy_047.png", "easy_048.png", "easy_049.png", "easy_050.png",
    "easy_051.png", "easy_052.png", "easy_053.png", "easy_054.png", "easy_055.png",
    "easy_056.png", "easy_057.png", "easy_058.png", "easy_059.png", "easy_060.png",
    "easy_061.png", "easy_062.png", "easy_063.png", "easy_064.png", "easy_065.png",
    "easy_066.png", "easy_067.png", "easy_068.png", "easy_069.png", "easy_070.png",
    "easy_071.png", "easy_072.png", "easy_073.png", "easy_074.png", "easy_075.png",
    "easy_076.png", "easy_077.png", "easy_078.png", "easy_079.png", "easy_080.png",
    "easy_081.png", "easy_082.png", "easy_083.png", "easy_084.png", "easy_085.png",
    "easy_086.png", "easy_087.png", "easy_088.png", "easy_089.png", "easy_090.png",
    "easy_091.png", "easy_092.png", "easy_093.png", "easy_094.png", "easy_095.png",
    "easy_096.png", "easy_097.png", "easy_098.png", "easy_099.png", "easy_100.png",
    "easy_101.png", "easy_102.png", "easy_103.png", "easy_104.png"
)

# 困难单词图片重命名映射
$hardImages = @(
    "hard_001.png", "hard_002.png", "hard_003.png", "hard_004.png", "hard_005.png",
    "hard_006.png", "hard_007.png", "hard_008.png", "hard_009.png", "hard_010.png",
    "hard_011.png", "hard_012.png", "hard_013.png", "hard_014.png", "hard_015.png",
    "hard_016.png", "hard_017.png", "hard_018.png", "hard_019.png", "hard_020.png",
    "hard_021.png", "hard_022.png", "hard_023.png", "hard_024.png", "hard_025.png",
    "hard_026.png", "hard_027.png", "hard_028.png", "hard_029.png", "hard_030.png",
    "hard_031.png", "hard_032.png", "hard_033.png", "hard_034.png", "hard_035.png",
    "hard_036.png", "hard_037.png", "hard_038.png", "hard_039.png", "hard_040.png",
    "hard_041.png", "hard_042.png", "hard_043.png", "hard_044.png", "hard_045.png",
    "hard_046.png", "hard_047.png", "hard_048.png", "hard_049.png", "hard_050.png",
    "hard_051.png", "hard_052.png", "hard_053.png", "hard_054.png", "hard_055.png",
    "hard_056.png", "hard_057.png", "hard_058.png", "hard_059.png", "hard_060.png",
    "hard_061.png", "hard_062.png", "hard_063.png", "hard_064.png", "hard_065.png",
    "hard_066.png", "hard_067.png", "hard_068.png", "hard_069.png", "hard_070.png",
    "hard_071.png", "hard_072.png", "hard_073.png", "hard_074.png", "hard_075.png",
    "hard_076.png", "hard_077.png", "hard_078.png", "hard_079.png", "hard_080.png",
    "hard_081.png", "hard_082.png", "hard_083.png", "hard_084.png", "hard_085.png",
    "hard_086.png", "hard_087.png", "hard_088.png", "hard_089.png"
)

# 处理简单单词图片
Write-Host "处理简单单词图片..." -ForegroundColor Yellow
$sourceEasyPath = "public\images\easy"
$targetEasyPath = "images\easy"

if (Test-Path $sourceEasyPath) {
    $easyFiles = Get-ChildItem -Path $sourceEasyPath -File | Sort-Object Name
    $index = 0
    
    foreach ($file in $easyFiles) {
        if ($index -lt $easyImages.Count) {
            $newName = $easyImages[$index]
            $sourcePath = $file.FullName
            $targetPath = Join-Path $targetEasyPath $newName
            
            try {
                Copy-Item -Path $sourcePath -Destination $targetPath -Force
                Write-Host "复制: $($file.Name) -> $newName" -ForegroundColor Green
                $index++
            } catch {
                Write-Host "错误: 无法复制 $($file.Name)" -ForegroundColor Red
            }
        }
    }
    Write-Host "简单单词图片处理完成，共处理 $index 张图片" -ForegroundColor Green
} else {
    Write-Host "错误: 找不到源文件夹 $sourceEasyPath" -ForegroundColor Red
}

# 处理困难单词图片
Write-Host "处理困难单词图片..." -ForegroundColor Yellow
$sourceHardPath = "public\images\hard"
$targetHardPath = "images\hard"

if (Test-Path $sourceHardPath) {
    $hardFiles = Get-ChildItem -Path $sourceHardPath -File | Sort-Object Name
    $index = 0
    
    foreach ($file in $hardFiles) {
        if ($index -lt $hardImages.Count) {
            $newName = $hardImages[$index]
            $sourcePath = $file.FullName
            $targetPath = Join-Path $targetHardPath $newName
            
            try {
                Copy-Item -Path $sourcePath -Destination $targetPath -Force
                Write-Host "复制: $($file.Name) -> $newName" -ForegroundColor Green
                $index++
            } catch {
                Write-Host "错误: 无法复制 $($file.Name)" -ForegroundColor Red
            }
        }
    }
    Write-Host "困难单词图片处理完成，共处理 $index 张图片" -ForegroundColor Green
} else {
    Write-Host "错误: 找不到源文件夹 $sourceHardPath" -ForegroundColor Red
}

Write-Host "图片重命名和整理完成！" -ForegroundColor Green
Write-Host "新的图片位置：" -ForegroundColor Cyan
Write-Host "- 简单单词：images/easy/" -ForegroundColor Cyan
Write-Host "- 困难单词：images/hard/" -ForegroundColor Cyan

Read-Host "按任意键退出"
