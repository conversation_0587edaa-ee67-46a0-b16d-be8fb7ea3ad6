@echo off
echo 开始复制和重命名图片...
echo.

REM 创建目标文件夹
if not exist "images\easy" mkdir "images\easy"
if not exist "images\hard" mkdir "images\hard"

echo 处理简单单词图片...
set /a counter=1

REM 复制并重命名简单单词图片
for %%f in ("public\images\easy\*.*") do (
    set "filename=easy_000!counter!.png"
    set "filename=!filename:~-7!"
    copy "%%f" "images\easy\!filename!" >nul
    echo 复制: %%~nxf -^> !filename!
    set /a counter+=1
)

echo.
echo 处理困难单词图片...
set /a counter=1

REM 复制并重命名困难单词图片
for %%f in ("public\images\hard\*.*") do (
    set "filename=hard_000!counter!.png"
    set "filename=!filename:~-7!"
    copy "%%f" "images\hard\!filename!" >nul
    echo 复制: %%~nxf -^> !filename!
    set /a counter+=1
)

echo.
echo 图片复制和重命名完成！
echo 新的图片位置：
echo - 简单单词：images\easy\
echo - 困难单词：images\hard\
echo.
pause
