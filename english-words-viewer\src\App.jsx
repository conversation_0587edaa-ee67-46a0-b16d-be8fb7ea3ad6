import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState('home') // 'home', 'easy', 'hard'
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [images, setImages] = useState([])
  const [loading, setLoading] = useState(false)

  // 加载图片列表
  const loadImages = async (difficulty) => {
    setLoading(true)
    try {
      const response = await fetch(`/${difficulty}-images.json`)
      const imageList = await response.json()
      setImages(imageList)
      setCurrentImageIndex(0)
      setCurrentView(difficulty)
    } catch (error) {
      console.error('加载图片列表失败:', error)
      alert('加载图片失败，请刷新页面重试')
    }
    setLoading(false)
  }

  // 键盘导航
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (currentView === 'easy' || currentView === 'hard') {
        if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
          goToPrevious()
        } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
          goToNext()
        } else if (e.key === 'Escape') {
          goHome()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [currentView, currentImageIndex, images.length])

  const goToNext = () => {
    if (currentImageIndex < images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1)
    }
  }

  const goToPrevious = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1)
    }
  }

  const goHome = () => {
    setCurrentView('home')
    setImages([])
    setCurrentImageIndex(0)
  }

  // 触摸手势支持
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)

  const minSwipeDistance = 50

  const onTouchStart = (e) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  if (currentView === 'home') {
    return (
      <div className="home">
        <div className="home-content">
          <h1>英语单词图片浏览器</h1>
          <p>选择难度开始学习</p>
          <div className="difficulty-buttons">
            <button
              className="difficulty-btn easy-btn"
              onClick={() => loadImages('easy')}
            >
              <span className="btn-icon">📚</span>
              <span className="btn-text">简单单词</span>
              <span className="btn-desc">基础词汇学习</span>
            </button>
            <button
              className="difficulty-btn hard-btn"
              onClick={() => loadImages('hard')}
            >
              <span className="btn-icon">🎓</span>
              <span className="btn-text">困难单词</span>
              <span className="btn-desc">进阶词汇挑战</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="error">
        <p>没有找到图片</p>
        <button onClick={goHome}>返回首页</button>
      </div>
    )
  }

  const currentImage = images[currentImageIndex]
  const progress = ((currentImageIndex + 1) / images.length) * 100

  return (
    <div
      className="viewer"
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
    >
      <div className="viewer-header">
        <button className="back-btn" onClick={goHome}>
          ← 返回
        </button>
        <div className="progress-info">
          <span className="difficulty-label">
            {currentView === 'easy' ? '简单' : '困难'}
          </span>
          <span className="page-info">
            {currentImageIndex + 1} / {images.length}
          </span>
        </div>
      </div>

      <div className="progress-bar">
        <div
          className="progress-fill"
          style={{ width: `${progress}%` }}
        ></div>
      </div>

      <div className="image-container">
        <img
          src={`/images/${currentView}/${currentImage}`}
          alt={`单词图片 ${currentImageIndex + 1}`}
          className="word-image"
          onError={(e) => {
            console.error('图片加载失败:', currentImage)
            e.target.style.display = 'none'
          }}
        />
      </div>

      <div className="navigation">
        <button
          className="nav-btn prev-btn"
          onClick={goToPrevious}
          disabled={currentImageIndex === 0}
        >
          ← 上一张
        </button>
        <button
          className="nav-btn next-btn"
          onClick={goToNext}
          disabled={currentImageIndex === images.length - 1}
        >
          下一张 →
        </button>
      </div>

      <div className="help-text">
        <p>💡 提示：可以使用键盘方向键或滑动手势翻页</p>
      </div>
    </div>
  )
}

export default App
