<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词图片浏览器 - 测试版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .home {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .home-content {
            text-align: center;
            color: white;
            max-width: 500px;
            width: 100%;
        }

        .home-content h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .home-content p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .difficulty-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .difficulty-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 40px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%;
            max-width: 300px;
            min-height: 120px;
            justify-content: center;
        }

        .difficulty-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .btn-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .btn-text {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .btn-desc {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .viewer {
            display: none;
            flex-direction: column;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .viewer.active {
            display: flex;
        }

        .viewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a67d8;
        }

        .progress-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            text-align: right;
        }

        .difficulty-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 2px;
        }

        .page-info {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .progress-bar {
            height: 4px;
            background: #e2e8f0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .image-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            min-height: 0;
        }

        .word-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 120px;
        }

        .prev-btn {
            background: #e2e8f0;
            color: #4a5568;
        }

        .prev-btn:not(:disabled):hover {
            background: #cbd5e0;
        }

        .next-btn {
            background: #667eea;
            color: white;
        }

        .next-btn:not(:disabled):hover {
            background: #5a67d8;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .help-text {
            text-align: center;
            padding: 10px 20px;
            background: white;
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .home-content h1 {
                font-size: 2rem;
            }
            
            .difficulty-btn {
                padding: 25px 30px;
                min-height: 100px;
            }
            
            .btn-icon {
                font-size: 2rem;
            }
            
            .btn-text {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div id="home" class="home">
        <div class="home-content">
            <h1>英语单词图片浏览器</h1>
            <p>选择难度开始学习</p>
            <div class="difficulty-buttons">
                <button class="difficulty-btn easy-btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📚</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习</span>
                </button>
                <button class="difficulty-btn hard-btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战</span>
                </button>
            </div>
        </div>
    </div>

    <div id="viewer" class="viewer">
        <div class="viewer-header">
            <button class="back-btn" onclick="goHome()">← 返回</button>
            <div class="progress-info">
                <span class="difficulty-label" id="difficulty-label">简单</span>
                <span class="page-info" id="page-info">1 / 1</span>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>

        <div class="image-container">
            <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
        </div>

        <div class="navigation">
            <button class="nav-btn prev-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
            <button class="nav-btn next-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
        </div>

        <div class="help-text">
            <p>💡 提示：可以使用键盘方向键翻页</p>
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];

        // 直接嵌入图片列表，避免AJAX请求问题
        const easyImages = [
            "1.单词这样记1 .png", "1.单词这样记2 .png", "1.最强记忆法1 .png", "1.最强记忆法2 .png",
            "10-麦当劳1 .png", "10-麦当劳2 .png", "10-麦当劳3 .png", "11-皮皮虾1 .png", "11-皮皮虾2 .png",
            "12-光棍1  .png", "12-光棍1 .png", "12-光棍2 .png", "13-吝啬鬼1 .png", "13-吝啬鬼2 .png",
            "13-吝啬鬼3 .png", "13-吝啬鬼4 .png", "14-桃花1 .PNG", "14-桃花2 .PNG", "14-桃花3 .PNG",
            "15-嫦娥1 .PNG", "15-嫦娥2 .PNG", "15-嫦娥3 .PNG", "16-日月星辰1更多课程lxknumber1 .PNG",
            "16-日月星辰2更多课程lxknumber1 .PNG", "17-男明星1 .PNG", "17-男明星2 .PNG", "17-男明星3 .PNG",
            "18-女明星1 .png", "18-女明星2 .png", "18-女明星3 .png", "19-歌手1 .png", "19-歌手2 .png",
            "19-歌手3 .png", "19-歌手4 .png", "2-女王 .png", "2-女王2 .png", "2-女王3 .png", "2-女王4 .png",
            "20-石头1 .png", "20-石头2 .png", "20-石头3 .png", "20-石头4 .png", "21-小猫1 .png",
            "21-小猫2 .png", "21-小猫3 .png", "22-街景1 .png", "22-街景2 .png", "22-街景3 .png",
            "22-街景4 .png", "22-街景5 .png", "23-战争1 .png", "23-战争2 .png", "23-战争3 .png",
            "24-学校1 .png", "24-学校2 .png", "24-学校3 .png", "24-学校4 .png", "25-工作1 .png",
            "25-工作2 .png", "25-工作3 .png", "25-工作4 .png", "26-吃货1更多课程lxknumber1 .png",
            "26-吃货2更多课程lxknumber1 .png", "26-吃货3更多课程lxknumber1 .png", "26-吃货4更多课程lxknumber1 .png",
            "27-家居1 .png", "27-家居2 .png", "27-家居3 .png", "28-小威1 .png", "28-小威2 .png",
            "28-小威3 .png", "28-小威4 .png", "29-化妆品1 .png", "29-化妆品2 .png", "3-佛1 .png",
            "3-佛2 .png", "30-汽车1 .png", "30-汽车2 .png", "31-手机1 .png", "31-手机2 .png",
            "31-手机3 .png", "32-人生1 .png", "32-人生2 .png", "33-熟词词根1 .png", "33-熟词词根2 .png",
            "33-熟词词根3 .png", "33-熟词词根4 .png", "34-后缀1 .png", "34-后缀2 .png", "34-后缀3  .png",
            "34-后缀4 .png", "35-前缀1 .png", "35-前缀2 .png", "4-白娘子1 .png", "4-白娘子2 .png",
            "4-白娘子3 .png", "4-白娘子4 .png", "5-小哥哥 .png", "5-小哥哥2 .png", "5-小哥哥3 .png",
            "6-小姐姐1更多课程lxknumber1 .png", "6-小姐姐2更多课程lxknumber1 .png", "7-爸妈1 .png",
            "7-爸妈2 .png", "7-爸妈3 .png", "7-爸妈4 .png", "8-弟弟1 .png", "8-弟弟2 .png",
            "8-弟弟3 .png", "8-弟弟4 .png", "8-弟弟5 .png", "9-歌舞1 .png", "9-歌舞2 .png", "9-歌舞3 .png"
        ];

        const hardImages = [
            "1.单词这么记 .PNG", "1.最强记忆法1 .PNG", "1.最强记忆法2 .PNG", "10.猫1 .PNG", "10.猫2 .PNG",
            "10.猫3 .PNG", "11.动物园1 .PNG", "11.动物园2 .PNG", "11.动物园3 .PNG", "12.太阳1 .PNG",
            "12.太阳2 .PNG", "13.门前1 .PNG", "13.门前2 .PNG", "14.风景1 .PNG", "14.风景2 .PNG",
            "15.小威（上）1 .PNG", "15.小威（上）2 .PNG", "15.小威（上）3 .PNG", "15.小威（上）4 .PNG",
            "16.小威（下）1更多课程lxknumber1 .PNG", "16.小威（下）2更多课程lxknumber1 .PNG", "16.小威（下）3更多课程lxknumber1 .PNG",
            "17.自然1 .PNG", "17.自然2 .PNG", "17.自然3 .PNG", "17.自然4 .PNG", "17.自然5 .PNG",
            "18.课堂1 .PNG", "18.课堂2 .PNG", "19.工人1 .PNG", "19.工人2 .PNG", "2.白娘子1 .PNG",
            "2.白娘子2 .PNG", "2.白娘子3 .PNG", "2.白娘子4 .PNG", "20.大牌1 .PNG", "20.大牌2 .PNG",
            "20.大牌3 .PNG", "20.大牌4 .PNG", "21.麦当劳1 .PNG", "21.麦当劳2 .PNG", "21.麦当劳3 .PNG",
            "22.零食1 .PNG", "22.零食2 .PNG", "22.零食3 .PNG", "22.零食4 .PNG", "23.买买提1 .PNG",
            "23.买买提2 .PNG", "23.买买提3 .PNG", "24.唱跳1 .PNG", "24.唱跳2 .PNG", "25.小东西1 .PNG",
            "25.小东西2 .PNG", "26.熟词词根1更多课程lxknumber1 .PNG", "26.熟词词根2更多课程lxknumber1 .PNG",
            "26.熟词词根3更多课程lxknumber1 .PNG", "26.熟词词根4更多课程lxknumber1 .PNG", "26.熟词词根5更多课程lxknumber1 .PNG",
            "26.熟词词根6更多课程lxknumber1 .PNG", "26.熟词词根7更多课程lxknumber1 .PNG", "27.词缀1 .PNG",
            "27.词缀2 .PNG", "27.词缀3 .PNG", "27.词缀4 .PNG", "27.词缀5 .PNG", "27.词缀6更多课程lxknumber1 .PNG",
            "3.嫦娥1 .PNG", "3.嫦娥2 .PNG", "3.嫦娥3 .PNG", "4.哥哥1 .PNG", "4.哥哥2 .PNG",
            "5.弟弟1 .PNG", "5.弟弟2 .PNG", "5.弟弟3 .PNG", "6.妈妈1更多课程lxknumber1 .PNG",
            "6.妈妈2更多课程lxknumber1 .PNG", "6.妈妈3更多课程lxknumber1 .PNG", "7.女王1 .PNG",
            "7.女王2 .PNG", "7.女王3 .PNG", "7.女王4 .PNG", "8.男明星1 .PNG", "8.男明星2 .PNG",
            "9.女明星1 .PNG", "9.女明星2 .PNG", "9.女明星3 .PNG", "9.女明星4 .PNG", "9.女明星5 .PNG"
        ];

        function loadImages(difficulty) {
            try {
                if (difficulty === 'easy') {
                    images = easyImages;
                } else {
                    images = hardImages;
                }

                currentImageIndex = 0;
                currentView = difficulty;

                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');

                updateViewer();
            } catch (error) {
                console.error('加载图片列表失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            const progress = ((currentImageIndex + 1) / images.length) * 100;
            
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            
            const imgElement = document.getElementById('word-image');
            imgElement.src = `images/${currentView}/${currentImage}`;
            imgElement.style.display = 'block';
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                this.style.display = 'none';
            };
            
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'flex';
            document.getElementById('viewer').classList.remove('active');
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            }
        });
    </script>
</body>
</html>
