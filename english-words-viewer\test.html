<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词图片浏览器 - 测试版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .home {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .home-content {
            text-align: center;
            color: white;
            max-width: 500px;
            width: 100%;
        }

        .home-content h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .home-content p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
        }

        .difficulty-buttons {
            display: flex;
            flex-direction: column;
            gap: 20px;
            align-items: center;
        }

        .difficulty-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 30px 40px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%;
            max-width: 300px;
            min-height: 120px;
            justify-content: center;
        }

        .difficulty-btn:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }

        .btn-icon {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .btn-text {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .btn-desc {
            font-size: 0.9rem;
            opacity: 0.7;
        }

        .viewer {
            display: none;
            flex-direction: column;
            min-height: 100vh;
            background: #f8f9fa;
        }

        .viewer.active {
            display: flex;
        }

        .viewer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .back-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: #5a67d8;
        }

        .progress-info {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            text-align: right;
        }

        .difficulty-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 2px;
        }

        .page-info {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .progress-bar {
            height: 4px;
            background: #e2e8f0;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: width 0.3s ease;
        }

        .image-container {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            min-height: 0;
        }

        .word-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            padding: 20px;
            background: white;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            min-width: 120px;
        }

        .prev-btn {
            background: #e2e8f0;
            color: #4a5568;
        }

        .prev-btn:not(:disabled):hover {
            background: #cbd5e0;
        }

        .next-btn {
            background: #667eea;
            color: white;
        }

        .next-btn:not(:disabled):hover {
            background: #5a67d8;
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .help-text {
            text-align: center;
            padding: 10px 20px;
            background: white;
            color: #666;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .home-content h1 {
                font-size: 2rem;
            }
            
            .difficulty-btn {
                padding: 25px 30px;
                min-height: 100px;
            }
            
            .btn-icon {
                font-size: 2rem;
            }
            
            .btn-text {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div id="home" class="home">
        <div class="home-content">
            <h1>英语单词图片浏览器</h1>
            <p>选择难度开始学习</p>
            <div class="difficulty-buttons">
                <button class="difficulty-btn easy-btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📚</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习</span>
                </button>
                <button class="difficulty-btn hard-btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战</span>
                </button>
            </div>
        </div>
    </div>

    <div id="viewer" class="viewer">
        <div class="viewer-header">
            <button class="back-btn" onclick="goHome()">← 返回</button>
            <div class="progress-info">
                <span class="difficulty-label" id="difficulty-label">简单</span>
                <span class="page-info" id="page-info">1 / 1</span>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
        </div>

        <div class="image-container">
            <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
        </div>

        <div class="navigation">
            <button class="nav-btn prev-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
            <button class="nav-btn next-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
        </div>

        <div class="help-text">
            <p>💡 提示：可以使用键盘方向键翻页</p>
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];

        async function loadImages(difficulty) {
            try {
                const response = await fetch(`${difficulty}-images.json`);
                const imageList = await response.json();
                images = imageList;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
            } catch (error) {
                console.error('加载图片列表失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            const progress = ((currentImageIndex + 1) / images.length) * 100;
            
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            document.getElementById('progress-fill').style.width = `${progress}%`;
            
            const imgElement = document.getElementById('word-image');
            imgElement.src = `images/${currentView}/${currentImage}`;
            imgElement.style.display = 'block';
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                this.style.display = 'none';
            };
            
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'flex';
            document.getElementById('viewer').classList.remove('active');
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            }
        });
    </script>
</body>
</html>
