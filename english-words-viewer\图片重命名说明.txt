图片重命名说明
================

🎯 目标
-------
将原始的中文图片名称重命名为英文，并移动到新的文件夹结构中。

📁 新的文件夹结构
-----------------
english-words-viewer/
├── images/
│   ├── easy/          # 简单单词图片
│   └── hard/          # 困难单词图片

🔄 重命名规则
-------------

简单单词图片（从 public/images/easy/ 复制到 images/easy/）：
按文件名排序，重命名为：
easy_001.png, easy_002.png, easy_003.png, ..., easy_104.png

困难单词图片（从 public/images/hard/ 复制到 images/hard/）：
按文件名排序，重命名为：
hard_001.png, hard_002.png, hard_003.png, ..., hard_089.png

📝 手动操作步骤
---------------

方法一：使用Windows资源管理器
1. 打开 public/images/easy/ 文件夹
2. 选择所有图片文件，按名称排序
3. 逐个复制到 images/easy/ 文件夹
4. 重命名为 easy_001.png, easy_002.png, 等等
5. 对 hard 文件夹重复相同操作

方法二：使用批量重命名工具
1. 下载批量重命名工具（如 Bulk Rename Utility）
2. 选择源文件夹中的所有图片
3. 设置重命名规则：easy_### 或 hard_###
4. 批量重命名并复制到目标文件夹

方法三：使用PowerShell（推荐）
1. 右键点击 copy-images.ps1 文件
2. 选择"使用PowerShell运行"
3. 如果出现执行策略错误，先运行：
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
4. 然后再运行脚本

🔧 已更新的内容
---------------
- ✅ HTML文件中的图片路径已更新为 images/easy/ 和 images/hard/
- ✅ 图片列表已更新为新的英文命名
- ✅ 图片宽度已调整为屏幕的70%
- ✅ 创建了新的文件夹结构

⚠️ 重要提醒
-----------
1. 请保持图片的原始顺序，按文件名排序
2. 确保所有图片都是PNG格式
3. 重命名完成后，可以删除 public/images/ 文件夹
4. 测试网站确保图片正确显示

🚀 完成后
---------
重命名完成后，直接双击以下文件即可使用：
- long-image-viewer.html（推荐，专为长图设计）
- viewer.html（普通版本）

图片现在将以70%的屏幕宽度显示，更适合阅读！
