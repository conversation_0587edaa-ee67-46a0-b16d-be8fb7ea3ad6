// 生成新的图片列表
const fs = require('fs');
const path = require('path');

// 生成简单单词图片列表
const easyImages = [];
for (let i = 1; i <= 104; i++) {
    easyImages.push(`easy_${i.toString().padStart(3, '0')}.png`);
}

// 生成困难单词图片列表
const hardImages = [];
for (let i = 1; i <= 89; i++) {
    hardImages.push(`hard_${i.toString().padStart(3, '0')}.png`);
}

// 保存到JSON文件
fs.writeFileSync('images/easy-images.json', JSON.stringify(easyImages, null, 2));
fs.writeFileSync('images/hard-images.json', JSON.stringify(hardImages, null, 2));

console.log('图片列表生成完成！');
console.log(`简单单词：${easyImages.length}张`);
console.log(`困难单词：${hardImages.length}张`);

// 输出数组供HTML使用
console.log('\n简单单词数组：');
console.log('const easyImages = [');
easyImages.forEach((img, index) => {
    const comma = index < easyImages.length - 1 ? ',' : '';
    console.log(`    "${img}"${comma}`);
});
console.log('];');

console.log('\n困难单词数组：');
console.log('const hardImages = [');
hardImages.forEach((img, index) => {
    const comma = index < hardImages.length - 1 ? ',' : '';
    console.log(`    "${img}"${comma}`);
});
console.log('];');
