Write-Host "开始复制和重命名图片..." -ForegroundColor Green

# 处理简单单词图片
Write-Host "处理简单单词图片..." -ForegroundColor Yellow
$easyFiles = Get-ChildItem -Path "public\images\easy" -File | Sort-Object Name
$counter = 1

foreach ($file in $easyFiles) {
    $newName = "easy_{0:D3}.png" -f $counter
    $targetPath = "images\easy\$newName"
    Copy-Item -Path $file.FullName -Destination $targetPath -Force
    Write-Host "复制: $($file.Name) -> $newName" -ForegroundColor Green
    $counter++
}

# 处理困难单词图片
Write-Host "处理困难单词图片..." -ForegroundColor Yellow
$hardFiles = Get-ChildItem -Path "public\images\hard" -File | Sort-Object Name
$counter = 1

foreach ($file in $hardFiles) {
    $newName = "hard_{0:D3}.png" -f $counter
    $targetPath = "images\hard\$newName"
    Copy-Item -Path $file.FullName -Destination $targetPath -Force
    Write-Host "复制: $($file.Name) -> $newName" -ForegroundColor Green
    $counter++
}

Write-Host "图片复制和重命名完成！" -ForegroundColor Green
Write-Host "新的图片位置：" -ForegroundColor Cyan
Write-Host "- 简单单词：images/easy/" -ForegroundColor Cyan
Write-Host "- 困难单词：images/hard/" -ForegroundColor Cyan
