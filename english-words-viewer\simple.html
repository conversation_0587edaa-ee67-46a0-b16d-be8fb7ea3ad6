<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词图片浏览器</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; color: #333; 
        }
        .container { max-width: 800px; margin: 0 auto; padding: 20px; }
        .home { text-align: center; color: white; padding: 50px 20px; }
        .home h1 { font-size: 2.5rem; margin-bottom: 20px; }
        .home p { font-size: 1.2rem; margin-bottom: 40px; }
        .btn { 
            display: inline-block; padding: 15px 30px; margin: 10px;
            background: white; color: #333; text-decoration: none;
            border-radius: 10px; font-size: 1.1rem; font-weight: bold;
            transition: transform 0.3s ease; cursor: pointer; border: none;
        }
        .btn:hover { transform: translateY(-3px); }
        .viewer { display: none; background: white; border-radius: 15px; overflow: hidden; }
        .viewer.active { display: block; }
        .header { 
            display: flex; justify-content: space-between; align-items: center;
            padding: 15px 20px; background: #f8f9fa; border-bottom: 1px solid #dee2e6;
        }
        .back-btn { 
            background: #667eea; color: white; padding: 8px 16px;
            border: none; border-radius: 5px; cursor: pointer;
        }
        .progress { font-weight: bold; }
        .image-container { 
            text-align: center; padding: 20px; min-height: 400px;
            display: flex; align-items: center; justify-content: center;
        }
        .word-image { 
            max-width: 100%; max-height: 500px; border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .navigation { 
            display: flex; justify-content: space-between; padding: 20px;
            background: #f8f9fa; border-top: 1px solid #dee2e6;
        }
        .nav-btn { 
            padding: 10px 20px; border: none; border-radius: 5px;
            cursor: pointer; font-size: 1rem;
        }
        .prev-btn { background: #6c757d; color: white; }
        .next-btn { background: #007bff; color: white; }
        .nav-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .error { color: red; text-align: center; padding: 20px; }
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .container { padding: 10px; }
            .navigation { flex-direction: column; gap: 10px; }
            .nav-btn { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="home" class="home">
            <h1>📚 英语单词图片浏览器</h1>
            <p>选择难度开始学习</p>
            <button class="btn" onclick="loadImages('easy')">📖 简单单词 (基础词汇)</button>
            <button class="btn" onclick="loadImages('hard')">🎓 困难单词 (进阶词汇)</button>
        </div>

        <div id="viewer" class="viewer">
            <div class="header">
                <button class="back-btn" onclick="goHome()">← 返回</button>
                <div class="progress" id="progress">1 / 1</div>
            </div>
            <div class="image-container">
                <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
                <div id="loading" style="display: none;">加载中...</div>
                <div id="error" class="error" style="display: none;">图片加载失败</div>
            </div>
            <div class="navigation">
                <button class="nav-btn prev-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
                <button class="nav-btn next-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
            </div>
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];

        // 简化的图片列表（前几张作为测试）
        const easyImages = [
            "1.单词这样记1 .png", "1.单词这样记2 .png", "1.最强记忆法1 .png", "1.最强记忆法2 .png",
            "10-麦当劳1 .png", "10-麦当劳2 .png", "10-麦当劳3 .png", "11-皮皮虾1 .png", "11-皮皮虾2 .png",
            "12-光棍1 .png", "12-光棍2 .png", "13-吝啬鬼1 .png", "13-吝啬鬼2 .png", "13-吝啬鬼3 .png"
        ];

        const hardImages = [
            "1.单词这么记 .PNG", "1.最强记忆法1 .PNG", "1.最强记忆法2 .PNG", "10.猫1 .PNG", "10.猫2 .PNG",
            "10.猫3 .PNG", "11.动物园1 .PNG", "11.动物园2 .PNG", "11.动物园3 .PNG", "12.太阳1 .PNG"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];
            
            document.getElementById('progress').textContent = 
                `${currentImageIndex + 1} / ${images.length} (${currentView === 'easy' ? '简单' : '困难'})`;
            
            const imgElement = document.getElementById('word-image');
            const loadingElement = document.getElementById('loading');
            const errorElement = document.getElementById('error');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            loadingElement.style.display = 'block';
            errorElement.style.display = 'none';
            
            // 尝试加载图片
            imgElement.onload = function() {
                loadingElement.style.display = 'none';
                imgElement.style.display = 'block';
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';
                errorElement.textContent = `图片加载失败: ${currentImage}`;
            };
            
            // 设置图片路径
            imgElement.src = `public/images/${currentView}/${currentImage}`;
            
            // 更新按钮状态
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'block';
            document.getElementById('viewer').classList.remove('active');
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                }
            }
        });

        // 触摸手势支持
        let touchStartX = null;
        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null) return;
            
            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchStartX - touchEndX;
            
            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    goToNext(); // 向左滑动，下一张
                } else {
                    goToPrevious(); // 向右滑动，上一张
                }
            }
            
            touchStartX = null;
        });
    </script>
</body>
</html>
