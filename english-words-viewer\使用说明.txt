英语单词长图浏览器 - 使用说明
=====================================

🚀 快速开始
-----------

方法一：长图专用浏览器（推荐）
1. 双击 long-image-viewer.html 文件
2. 专为390x16371像素长图设计

方法二：普通浏览器
1. 双击 viewer.html 文件
2. 支持长图滚动查看

方法三：使用HTTP服务器
1. 双击 start.bat 启动服务器
2. 在浏览器中访问 http://localhost:9000/long-image-viewer.html

🎯 功能特点
-----------
- 双难度模式：简单单词 vs 困难单词
- 长图专用：专为390x16371像素长图优化
- 翻页浏览：一张图片一页，支持上一张/下一张
- 长图滚动：上下滚动查看完整长图内容
- 多种操作方式：
  * 点击按钮
  * 键盘方向键（←→切换图片，↑↓滚动图片）
  * 手机滑动手势（左右切换，上下滚动）
  * ESC键返回首页
  * 点击图片隐藏/显示控制栏
  * 双击图片重置到顶部
- 进度显示：实时显示当前页码和总页数
- 全屏模式：支持全屏浏览
- 响应式设计：完美适配手机和电脑
- 沉浸式体验：黑色背景，专注阅读

📱 操作说明
-----------

桌面端：
- 使用键盘左右方向键切换图片
- 使用键盘上下方向键滚动长图
- 点击"上一张"/"下一张"按钮
- 按ESC键返回首页
- 按空格键隐藏/显示控制栏
- 双击图片重置到顶部
- 点击全屏按钮进入全屏模式

手机端：
- 左右滑动切换图片
- 上下滑动查看长图内容
- 点击图片隐藏/显示控制栏
- 双击图片重置到顶部
- 支持触摸手势操作

🔧 故障排除
-----------

图片无法显示：
1. 确保图片文件在 public/images/easy/ 和 public/images/hard/ 文件夹中
2. 检查图片文件名是否与代码中的列表匹配
3. 尝试使用HTTP服务器而不是直接打开HTML文件

服务器无法启动：
1. 确保已安装Python或Node.js
2. 尝试手动运行：python -m http.server 9000
3. 或者直接双击 viewer.html 文件

📊 图片统计
-----------
- 简单单词：104张图片
- 困难单词：89张图片
- 总计：193张图片

🎨 自定义
---------
如需添加更多图片：
1. 将图片放入对应的文件夹（easy或hard）
2. 在 viewer.html 中的 easyImages 或 hardImages 数组中添加文件名
3. 刷新页面即可

💡 重要文件
-----------
- long-image-viewer.html - 长图专用浏览器（推荐使用）
- viewer.html - 普通浏览器文件
- start.bat - Windows启动脚本
- public/images/easy/ - 简单单词图片文件夹（390x16371像素长图）
- public/images/hard/ - 困难单词图片文件夹（390x16371像素长图）

🔍 图片规格
-----------
- 图片尺寸：390 x 16371 像素（超长垂直图片）
- 图片格式：PNG
- 图片内容：英语单词学习材料
- 适合：手机竖屏浏览，需要滚动查看完整内容

📄 许可证
---------
本项目仅供学习使用。
