英语单词图片浏览器 - 使用说明
=====================================

🚀 快速开始
-----------

方法一：直接打开HTML文件（推荐）
1. 双击 viewer.html 文件
2. 或者在浏览器中打开 viewer.html

方法二：使用HTTP服务器
1. 双击 start.bat 启动服务器
2. 在浏览器中访问 http://localhost:9000/viewer.html

🎯 功能特点
-----------
- 双难度模式：简单单词 vs 困难单词
- 翻页浏览：一张图片一页，支持上一张/下一张
- 多种操作方式：
  * 点击按钮
  * 键盘方向键（←→↑↓）
  * 手机滑动手势
  * ESC键返回首页
- 进度显示：实时显示当前页码和总页数
- 响应式设计：完美适配手机和电脑
- 美观界面：现代化设计，渐变背景

📱 操作说明
-----------

桌面端：
- 使用键盘方向键翻页
- 点击"上一张"/"下一张"按钮
- 按ESC键返回首页

手机端：
- 左右滑动翻页
- 点击按钮操作
- 支持触摸手势

🔧 故障排除
-----------

图片无法显示：
1. 确保图片文件在 public/images/easy/ 和 public/images/hard/ 文件夹中
2. 检查图片文件名是否与代码中的列表匹配
3. 尝试使用HTTP服务器而不是直接打开HTML文件

服务器无法启动：
1. 确保已安装Python或Node.js
2. 尝试手动运行：python -m http.server 9000
3. 或者直接双击 viewer.html 文件

📊 图片统计
-----------
- 简单单词：104张图片
- 困难单词：89张图片
- 总计：193张图片

🎨 自定义
---------
如需添加更多图片：
1. 将图片放入对应的文件夹（easy或hard）
2. 在 viewer.html 中的 easyImages 或 hardImages 数组中添加文件名
3. 刷新页面即可

💡 重要文件
-----------
- viewer.html - 主要的浏览器文件（推荐使用）
- start.bat - Windows启动脚本
- public/images/easy/ - 简单单词图片文件夹
- public/images/hard/ - 困难单词图片文件夹

📄 许可证
---------
本项目仅供学习使用。
