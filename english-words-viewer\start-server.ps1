# 启动简单的HTTP服务器
$port = 8080
$path = Get-Location

Write-Host "启动HTTP服务器..."
Write-Host "端口: $port"
Write-Host "路径: $path"
Write-Host "访问地址: http://localhost:$port/test.html"
Write-Host ""
Write-Host "按 Ctrl+C 停止服务器"
Write-Host ""

# 尝试使用Python启动服务器
try {
    python -m http.server $port
} catch {
    Write-Host "Python不可用，尝试使用Node.js..."
    try {
        npx http-server . -p $port -o
    } catch {
        Write-Host "无法启动服务器。请确保安装了Python或Node.js。"
        Read-Host "按任意键退出"
    }
}
